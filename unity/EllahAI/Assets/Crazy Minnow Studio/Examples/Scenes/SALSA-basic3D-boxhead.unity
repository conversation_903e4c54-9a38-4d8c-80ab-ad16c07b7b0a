%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 1
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 0
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 500
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 500
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 4890085278179872738, guid: 1d2132f88020e49c996d7bdbe2867e0a, type: 2}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &10140498
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 10140499}
  m_Layer: 0
  m_Name: boxHead.v2_boxHead.v2_Gizmo_eyeRefFwd_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &10140499
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 10140498}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: 1.037}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 580689203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &119433646
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 119433647}
  - component: {fileID: 119433648}
  m_Layer: 0
  m_Name: boxHead.v2_root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &119433647
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 119433646}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 1, z: 0, w: 0}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 580689203}
  - {fileID: 751161432}
  - {fileID: 2035328005}
  - {fileID: 1817026201}
  - {fileID: 1011782749}
  - {fileID: 1206537976}
  - {fileID: 542351513}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &119433648
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 119433646}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7d9b384a0ccedf04d983816c1ae6ed0d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  salsa: {fileID: 580689207}
--- !u!1 &146297974
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 146297975}
  m_Layer: 0
  m_Name: boxHead.v2_boxHead.v2_Gizmo_eyeRefFix_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &146297975
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 146297974}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: 1.037}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 580689203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &224134272
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 224134274}
  - component: {fileID: 224134273}
  m_Layer: 0
  m_Name: boxHead.v2_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &224134273
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 224134272}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1382872388, guid: 21fe281538940584e84b6d5eedb3525e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  inspStateFoldoutSettings: 1
  showGizmo: 1
--- !u!4 &224134274
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 224134272}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: 1.037}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 580689203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &310533620
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 310533621}
  m_Layer: 0
  m_Name: boxHead.v2_boxHead.v2_Gizmo_eyeRefFwd_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &310533621
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 310533620}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: 1.037}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 580689203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &519288684
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 519288686}
  - component: {fileID: 519288685}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &519288685
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 519288684}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 1
  m_Color: {r: 1, g: 0.95686275, b: 0.8392157, a: 1}
  m_Intensity: 0.7
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!4 &519288686
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 519288684}
  serializedVersion: 2
  m_LocalRotation: {x: 0.21621507, y: -0.2522516, z: 0.057934646, w: 0.94141585}
  m_LocalPosition: {x: 0, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 25.87, y: -30.000002, z: 0}
--- !u!1 &542351512
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 542351513}
  m_Layer: 0
  m_Name: boxHead.v2_boxHead.v2_headRefFix_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &542351513
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 542351512}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 119433647}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &574139585
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 574139586}
  m_Layer: 0
  m_Name: boxHead.v2_boxHead.v2_Gizmo_eyeRefFwd_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &574139586
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 574139585}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: 1.037}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 580689203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &580689202 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 100000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
  m_PrefabInstance: {fileID: 1958638727}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &580689203 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
  m_PrefabInstance: {fileID: 1958638727}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &580689204
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 580689202}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 974247159, guid: 21fe281538940584e84b6d5eedb3525e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  inspStateFoldoutReferences: 0
  inspStateFoldoutHeads: 0
  inspStateFoldoutHeadProps: 1
  inspStateFoldoutAnimBones: 0
  inspStateFoldoutEyes: 0
  inspStateFoldoutEyeProps: 1
  inspStateFoldoutEyelids: 0
  inspStateFoldoutEyelidBlinkProps: 1
  inspStateFoldoutEyelidTrackProps: 1
  drawEyeBoneCaptureControls: 0
  drawBlinkBoneCaptureControls: 0
  drawTrackBoneCaputreControls: 0
  headIsRoot: 0
  configReadyHead: 1
  configReadyEye: 1
  configReadyBlink: 1
  configReadyTrack: 1
  characterRoot: {fileID: 119433647}
  queueProcessor: {fileID: 580689205}
  lookTarget: {fileID: 0}
  useAffinity: 0
  affinityPercentage: 0.75
  hasAffinity: 1
  affinityTimerRange: {x: 2, y: 5}
  updateDelay: 0.07
  useSpriteFlipping: 0
  flipType: 0
  flipScale: {fileID: 0}
  flipSprite: {fileID: 0}
  parentRandTargets: 0
  showSceneGizmos: 1
  warnOnNullRefs: 1
  flipState: 0
  animCancel: []
  headEnabled: 1
  headTemplate: 1
  head2DProcessing: 0
  headTargetOffset: {x: 0, y: 1.4, z: 0}
  headClamp: {x: 70, y: 140, z: 45}
  headFacingCamera: 0
  headRandom: 1
  headRandomFov: {x: 5, y: 45, z: 22.5}
  headRandFovHalf: {x: 0, y: 0, z: 0}
  headRandTimerRange: {x: 5, y: 10}
  headRandTimer: 11.2218685
  headUseDistExtents: 0
  headRandDistRange: {x: 3, y: 3}
  updateHeadFwdCenterRefs: 1
  headTargetRadius: 0.05
  headBones:
  - trans:
    - {fileID: 580689203}
  headRefsFwd:
  - trans:
    - {fileID: 1206537976}
    offset:
    - {x: 0, y: 0, z: 0, w: 1}
  headRefsFix:
  - trans:
    - {fileID: 542351513}
  headRefCenter: {fileID: 1011782749}
  newHeadDirs:
  - dir:
    - vec: {x: -0, y: 0, z: 0}
      quat: {x: -0, y: 1, z: -0, w: 0}
  headTarget: {fileID: 2121294016}
  headTarget2D: {x: 0, y: 0, z: 0}
  heads:
  - expData:
      name: head
      components:
      - name: head
        controlType: 1
        lipsyncControlType: 0
        emoteControlType: 0
        eyesControlType: 1
        durationDelay: 0
        durationOn: 0.75
        durationHold: 0
        durationOff: 0
        isSmoothDisable: 0
        isPersistent: 1
        expressionType: 2
        easing: 4
        expressionHandler: 0
        isAnimatorControlled: 0
        useOffset: 0
        useOffsetFollow: 1
        inspFoldout: 1
        enabled: 1
        isBonePreviewUpdated: 0
        frac: 1
        directionType: 0
      controllerVars:
      - bone: {fileID: 580689203}
        baseTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: -0, y: -0, z: -0, w: 1}
          scale: {x: 1, y: 1, z: 1}
        startTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: -0, y: -0, z: -0, w: 1}
          scale: {x: 1, y: 1, z: 1}
        endTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: -0, y: -0, z: -0, w: 1}
          scale: {x: 1, y: 1, z: 1}
        fracPos: 0
        fracRot: 1
        fracScl: 1
        inspIsSetStart: 1
        inspIsSetEnd: 1
        umaUepProxy: {fileID: 0}
        uepAmount: 0
        smr: {fileID: 0}
        blendIndex: 0
        minShape: 0
        maxShape: 1
        eyeGizmo: {fileID: 0}
        eventSender: {fileID: 0}
        animator: {fileID: 0}
        isTriggerParameterBiDirectional: 0
        eventIdentityName: 
        onState: 2
        display2dImage: 0
        isRestNull: 0
        spriteRenderer: {fileID: 0}
        sprites: []
        uguiRenderer: {fileID: 0}
        textureRenderer: {fileID: 0}
        materialIndex: 0
        backupTextures: []
        textures: []
        materialRenderer: {fileID: 0}
        materials: []
      inspFoldout: 0
      previewDisplayMode: 0
      collectionExpand: 0
    gizmo: {fileID: 0}
    referenceIdx: 0
  headTarget2DDot: 0
  eyeEnabled: 1
  eyeTemplate: 3
  sectorCount: 3
  prevSectorCount: 0
  eyeControlType: 0
  forwardSectorRadius: 1
  eye2DProcessing: 0
  eye2DDepth: 1
  eyePosPercentX: 0
  eyePosPercentY: 0
  useEyeShapes: 1
  useEyeSectors: 0
  eyeClamp: {x: 25, y: 45, z: 0}
  eyeRandom: 1
  eyeRandomFov: {x: 2.5, y: 5, z: 10}
  eyeRandTrackFov: {x: 0.4, y: 0.2}
  eyeRandTrackFovAffinity: {x: 0.2, y: 0.1}
  eyeRandTimerRange: {x: 0.25, y: 1}
  eyeRandTimer: 6.7573476
  eyeUseDistExtents: 0
  eyeRandDistRange: {x: 3, y: 3}
  eyeTargetRadius: 0.05
  eyeBones:
  - trans:
    - {fileID: 224134274}
    - {fileID: 224134274}
    - {fileID: 224134274}
    - {fileID: 224134274}
  eyeRefsFwd:
  - trans:
    - {fileID: 1709113804}
    - {fileID: 574139586}
    - {fileID: 785795840}
    - {fileID: 1678223854}
    offset:
    - {x: 0, y: 0, z: 0, w: 1}
    - {x: 0, y: 0, z: 0, w: 1}
    - {x: 0, y: 0, z: 0, w: 1}
    - {x: 0, y: 0, z: 0, w: 1}
  eyeRefsFix:
  - trans:
    - {fileID: 846113221}
    - {fileID: 1705847270}
    - {fileID: 146297975}
    - {fileID: 2083864910}
  eyeRefCenter: {fileID: 1893005325}
  newEyeDirs:
  - dir:
    - vec: {x: -0, y: 0, z: 0}
      quat: {x: -0, y: 1, z: -0, w: 0}
    - vec: {x: -0, y: 0, z: 0}
      quat: {x: -0, y: 1, z: -0, w: 0}
    - vec: {x: -0, y: 0, z: 0}
      quat: {x: -0, y: 1, z: -0, w: 0}
    - vec: {x: -0, y: 0, z: 0}
      quat: {x: -0, y: 1, z: -0, w: 0}
  eyeTarget: {fileID: 1004491943}
  eyeTarget2D: {x: 0, y: 0, z: 0}
  eyes:
  - expData:
      name: eye 0
      components:
      - name: eye 0
        controlType: 0
        lipsyncControlType: 0
        emoteControlType: 0
        eyesControlType: 0
        durationDelay: 0
        durationOn: 0.1
        durationHold: 0
        durationOff: 0
        isSmoothDisable: 0
        isPersistent: 1
        expressionType: 3
        easing: 4
        expressionHandler: 0
        isAnimatorControlled: 0
        useOffset: 0
        useOffsetFollow: 1
        inspFoldout: 1
        enabled: 1
        isBonePreviewUpdated: 0
        frac: 1
        directionType: 2
      - name: eye 1
        controlType: 0
        lipsyncControlType: 0
        emoteControlType: 0
        eyesControlType: 0
        durationDelay: 0
        durationOn: 0.1
        durationHold: 0
        durationOff: 0
        isSmoothDisable: 0
        isPersistent: 1
        expressionType: 3
        easing: 4
        expressionHandler: 0
        isAnimatorControlled: 0
        useOffset: 0
        useOffsetFollow: 1
        inspFoldout: 1
        enabled: 1
        isBonePreviewUpdated: 0
        frac: 1
        directionType: 6
      - name: eye 2
        controlType: 0
        lipsyncControlType: 0
        emoteControlType: 0
        eyesControlType: 0
        durationDelay: 0
        durationOn: 0.1
        durationHold: 0
        durationOff: 0
        isSmoothDisable: 0
        isPersistent: 1
        expressionType: 3
        easing: 4
        expressionHandler: 0
        isAnimatorControlled: 0
        useOffset: 0
        useOffsetFollow: 1
        inspFoldout: 1
        enabled: 1
        isBonePreviewUpdated: 0
        frac: 1
        directionType: 8
      - name: eye 3
        controlType: 0
        lipsyncControlType: 0
        emoteControlType: 0
        eyesControlType: 0
        durationDelay: 0
        durationOn: 0.1
        durationHold: 0
        durationOff: 0
        isSmoothDisable: 0
        isPersistent: 1
        expressionType: 3
        easing: 4
        expressionHandler: 0
        isAnimatorControlled: 0
        useOffset: 0
        useOffsetFollow: 1
        inspFoldout: 1
        enabled: 1
        isBonePreviewUpdated: 0
        frac: 1
        directionType: 4
      controllerVars:
      - bone: {fileID: 224134274}
        baseTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        startTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        endTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        fracPos: 1
        fracRot: 0
        fracScl: 0
        inspIsSetStart: 0
        inspIsSetEnd: 0
        umaUepProxy: {fileID: 0}
        uepAmount: 0
        smr: {fileID: 580689209}
        blendIndex: 4
        minShape: 0
        maxShape: 1
        eyeGizmo: {fileID: 224134273}
        eventSender: {fileID: 0}
        animator: {fileID: 0}
        isTriggerParameterBiDirectional: 0
        eventIdentityName: 
        onState: 2
        display2dImage: 0
        isRestNull: 0
        spriteRenderer: {fileID: 0}
        sprites: []
        uguiRenderer: {fileID: 0}
        textureRenderer: {fileID: 0}
        materialIndex: 0
        backupTextures: []
        textures: []
        materialRenderer: {fileID: 0}
        materials: []
      - bone: {fileID: 224134274}
        baseTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        startTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        endTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        fracPos: 1
        fracRot: 0
        fracScl: 0
        inspIsSetStart: 0
        inspIsSetEnd: 0
        umaUepProxy: {fileID: 0}
        uepAmount: 0
        smr: {fileID: 580689209}
        blendIndex: 7
        minShape: 0
        maxShape: 1
        eyeGizmo: {fileID: 224134273}
        eventSender: {fileID: 0}
        animator: {fileID: 0}
        isTriggerParameterBiDirectional: 0
        eventIdentityName: 
        onState: 2
        display2dImage: 0
        isRestNull: 0
        spriteRenderer: {fileID: 0}
        sprites: []
        uguiRenderer: {fileID: 0}
        textureRenderer: {fileID: 0}
        materialIndex: 0
        backupTextures: []
        textures: []
        materialRenderer: {fileID: 0}
        materials: []
      - bone: {fileID: 224134274}
        baseTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        startTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        endTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        fracPos: 1
        fracRot: 0
        fracScl: 0
        inspIsSetStart: 0
        inspIsSetEnd: 0
        umaUepProxy: {fileID: 0}
        uepAmount: 0
        smr: {fileID: 580689209}
        blendIndex: 5
        minShape: 0
        maxShape: 1
        eyeGizmo: {fileID: 224134273}
        eventSender: {fileID: 0}
        animator: {fileID: 0}
        isTriggerParameterBiDirectional: 0
        eventIdentityName: 
        onState: 2
        display2dImage: 0
        isRestNull: 0
        spriteRenderer: {fileID: 0}
        sprites: []
        uguiRenderer: {fileID: 0}
        textureRenderer: {fileID: 0}
        materialIndex: 0
        backupTextures: []
        textures: []
        materialRenderer: {fileID: 0}
        materials: []
      - bone: {fileID: 224134274}
        baseTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        startTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        endTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        fracPos: 1
        fracRot: 0
        fracScl: 0
        inspIsSetStart: 0
        inspIsSetEnd: 0
        umaUepProxy: {fileID: 0}
        uepAmount: 0
        smr: {fileID: 580689209}
        blendIndex: 6
        minShape: 0
        maxShape: 1
        eyeGizmo: {fileID: 224134273}
        eventSender: {fileID: 0}
        animator: {fileID: 0}
        isTriggerParameterBiDirectional: 0
        eventIdentityName: 
        onState: 2
        display2dImage: 0
        isRestNull: 0
        spriteRenderer: {fileID: 0}
        sprites: []
        uguiRenderer: {fileID: 0}
        textureRenderer: {fileID: 0}
        materialIndex: 0
        backupTextures: []
        textures: []
        materialRenderer: {fileID: 0}
        materials: []
      inspFoldout: 1
      previewDisplayMode: 0
      collectionExpand: 0
    gizmo: {fileID: 224134273}
    referenceIdx: 0
  eyeExpIndex: 0
  eyeExpNames:
  - eye 0
  eyeExpRend: {fileID: 0}
  eyeExpRendItemCount: 2
  eyeExpRendIsNullOrSmr: 1
  blinkEnabled: 1
  eyelidTemplate: 3
  eyelidSelection: 1
  eyelidControlType: 0
  eyelid2DProcessing: 0
  blinkOn: 0
  blinkHold: 0
  blinkOff: 0
  blinkRandom: 1
  blinkRandTimerRange: {x: 0.5, y: 5}
  blinkRandTimer: 10.517986
  eyelidPercentEyes: 0.5
  trackPercent: 0
  blinklids:
  - expData:
      name: eyelid 0
      components:
      - name: eyelid 0
        controlType: 0
        lipsyncControlType: 0
        emoteControlType: 0
        eyesControlType: 1
        durationDelay: 0
        durationOn: 0.1
        durationHold: 0
        durationOff: 0.1
        isSmoothDisable: 0
        isPersistent: 0
        expressionType: 4
        easing: 4
        expressionHandler: 1
        isAnimatorControlled: 0
        useOffset: 0
        useOffsetFollow: 1
        inspFoldout: 1
        enabled: 1
        isBonePreviewUpdated: 0
        frac: 1
        directionType: 2
      controllerVars:
      - bone: {fileID: 0}
        baseTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        startTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        endTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        fracPos: 1
        fracRot: 0
        fracScl: 0
        inspIsSetStart: 0
        inspIsSetEnd: 0
        umaUepProxy: {fileID: 0}
        uepAmount: 0
        smr: {fileID: 580689209}
        blendIndex: 8
        minShape: 0
        maxShape: 1
        eyeGizmo: {fileID: 0}
        eventSender: {fileID: 0}
        animator: {fileID: 0}
        isTriggerParameterBiDirectional: 0
        eventIdentityName: 
        onState: 2
        display2dImage: 0
        isRestNull: 0
        spriteRenderer: {fileID: 0}
        sprites: []
        uguiRenderer: {fileID: 0}
        textureRenderer: {fileID: 0}
        materialIndex: 0
        backupTextures: []
        textures: []
        materialRenderer: {fileID: 0}
        materials: []
      inspFoldout: 1
      previewDisplayMode: 0
      collectionExpand: 0
    gizmo: {fileID: 0}
    referenceIdx: 0
  eyelidExpIndex: 0
  eyelidExpNames:
  - eyelid 0
  eyelidExpRend: {fileID: 0}
  eyelidExpRendItemCount: 1
  eyelidExpRendIsNullOrSmr: 1
  trackEnabled: 1
  trackBones: []
  tracklids: []
--- !u!114 &580689205
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 580689202}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 2069102741, guid: 21fe281538940584e84b6d5eedb3525e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  useMergeWithInfluencer: 1
  ignoreScaledTime: 0
  inspHeight: 150
  inspShowHeads: 1
  inspShowEmote: 1
  inspShowEyes: 1
  inspShowLids: 1
  inspShowLipsync: 1
  inspShowBlinks: 1
--- !u!82 &580689206
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 580689202}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 8300000, guid: be7ce50a3d5c057459eba3f954cdbff3, type: 3}
  m_Resource: {fileID: 8300000, guid: be7ce50a3d5c057459eba3f954cdbff3, type: 3}
  m_PlayOnAwake: 1
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 0
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 0
--- !u!114 &580689207
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 580689202}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -1091098677, guid: 21fe281538940584e84b6d5eedb3525e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  audioSrc: {fileID: 580689206}
  waitForAudioSource: 0
  useExternalAnalysis: 0
  audioSourceWaitTarget: {fileID: 0}
  queueProcessor: {fileID: 580689205}
  emoter: {fileID: 580689208}
  emphasizerTrigger: 0
  analysisValue: 0
  sampleSize: 512
  audioUpdateDelay: 0.0875
  playheadBias: 2800
  autoAdjustAnalysis: 1
  autoAdjustMicrophone: 0
  microphoneRecordHeadPointer: 0
  scaleExternalAnalysis: 0
  silencePulseThreshold: 3
  loCutoff: 0.015
  hiCutoff: 0.75
  useAdvDyn: 1
  advDynPrimaryBias: 0.5
  useAdvDynJitter: 1
  advDynJitterAmount: 0.1
  advDynJitterProb: 0.2
  useAdvDynSecondaryMix: 0
  advDynSecondaryMix: 0
  useAdvDynRollback: 0
  advDynRollback: 0.3
  globalFrac: 1
  usePersistence: 0
  useTimingsOverride: 1
  globalDurON: 0.12
  globalDurOFF: 0.07
  globalDurOffBalance: -0.41666663
  originalUpdateDelay: 0.08
  globalNuanceBalance: -0.204
  useEasingOverride: 0
  globalEasing: 4
  visemes:
  - expData:
      name: saySmall
      components:
      - name: saySml cmpnt 0
        controlType: 0
        lipsyncControlType: 0
        emoteControlType: 0
        eyesControlType: 1
        durationDelay: 0
        durationOn: 0.12
        durationHold: 0
        durationOff: 0.06
        isSmoothDisable: 0
        isPersistent: 0
        expressionType: 0
        easing: 4
        expressionHandler: 0
        isAnimatorControlled: 0
        useOffset: 0
        useOffsetFollow: 1
        inspFoldout: 0
        enabled: 1
        isBonePreviewUpdated: 0
        frac: 1
        directionType: 0
      controllerVars:
      - bone: {fileID: 0}
        baseTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        startTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        endTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        fracPos: 1
        fracRot: 1
        fracScl: 1
        inspIsSetStart: 0
        inspIsSetEnd: 0
        umaUepProxy: {fileID: 0}
        uepAmount: 0
        smr: {fileID: 580689209}
        blendIndex: 1
        minShape: 0
        maxShape: 1
        eyeGizmo: {fileID: 0}
        eventSender: {fileID: 0}
        animator: {fileID: 0}
        isTriggerParameterBiDirectional: 0
        eventIdentityName: 
        onState: 2
        display2dImage: 0
        isRestNull: 0
        spriteRenderer: {fileID: 0}
        sprites: []
        uguiRenderer: {fileID: 0}
        textureRenderer: {fileID: 0}
        materialIndex: 0
        backupTextures: []
        textures: []
        materialRenderer: {fileID: 0}
        materials: []
      inspFoldout: 0
      previewDisplayMode: 0
      collectionExpand: 0
    trigger: 0
  - expData:
      name: sayMedium
      components:
      - name: sayMed cmpnt 0
        controlType: 0
        lipsyncControlType: 0
        emoteControlType: 0
        eyesControlType: 1
        durationDelay: 0
        durationOn: 0.12
        durationHold: 0
        durationOff: 0.06
        isSmoothDisable: 0
        isPersistent: 0
        expressionType: 0
        easing: 4
        expressionHandler: 0
        isAnimatorControlled: 0
        useOffset: 0
        useOffsetFollow: 1
        inspFoldout: 0
        enabled: 1
        isBonePreviewUpdated: 0
        frac: 1
        directionType: 0
      controllerVars:
      - bone: {fileID: 0}
        baseTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        startTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        endTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        fracPos: 1
        fracRot: 1
        fracScl: 1
        inspIsSetStart: 0
        inspIsSetEnd: 0
        umaUepProxy: {fileID: 0}
        uepAmount: 0
        smr: {fileID: 580689209}
        blendIndex: 2
        minShape: 0
        maxShape: 1
        eyeGizmo: {fileID: 0}
        eventSender: {fileID: 0}
        animator: {fileID: 0}
        isTriggerParameterBiDirectional: 0
        eventIdentityName: 
        onState: 2
        display2dImage: 0
        isRestNull: 0
        spriteRenderer: {fileID: 0}
        sprites: []
        uguiRenderer: {fileID: 0}
        textureRenderer: {fileID: 0}
        materialIndex: 0
        backupTextures: []
        textures: []
        materialRenderer: {fileID: 0}
        materials: []
      inspFoldout: 0
      previewDisplayMode: 0
      collectionExpand: 0
    trigger: 0.11111112
  - expData:
      name: sayLarge
      components:
      - name: sayLrg cmpnt 0
        controlType: 0
        lipsyncControlType: 0
        emoteControlType: 0
        eyesControlType: 1
        durationDelay: 0
        durationOn: 0.12
        durationHold: 0
        durationOff: 0.06
        isSmoothDisable: 0
        isPersistent: 0
        expressionType: 0
        easing: 4
        expressionHandler: 0
        isAnimatorControlled: 0
        useOffset: 0
        useOffsetFollow: 1
        inspFoldout: 0
        enabled: 1
        isBonePreviewUpdated: 0
        frac: 1
        directionType: 0
      controllerVars:
      - bone: {fileID: 0}
        baseTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        startTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        endTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        fracPos: 1
        fracRot: 1
        fracScl: 1
        inspIsSetStart: 0
        inspIsSetEnd: 0
        umaUepProxy: {fileID: 0}
        uepAmount: 0
        smr: {fileID: 580689209}
        blendIndex: 3
        minShape: 0
        maxShape: 1
        eyeGizmo: {fileID: 0}
        eventSender: {fileID: 0}
        animator: {fileID: 0}
        isTriggerParameterBiDirectional: 0
        eventIdentityName: 
        onState: 2
        display2dImage: 0
        isRestNull: 0
        spriteRenderer: {fileID: 0}
        sprites: []
        uguiRenderer: {fileID: 0}
        textureRenderer: {fileID: 0}
        materialIndex: 0
        backupTextures: []
        textures: []
        materialRenderer: {fileID: 0}
        materials: []
      inspFoldout: 0
      previewDisplayMode: 0
      collectionExpand: 0
    trigger: 0.44444448
  configReady: 1
  configNotReadyNotified: 0
  warnOnNullRefs: 1
  isDebug: 0
  useAudioAnalysis: 1
  needAudioSource: 1
  inspStateFoldoutProcessing: 1
  inspStateFoldoutSettings: 0
  inspStateFoldoutVisemes: 0
  inspCollectionDisplayMode: 0
  inspAnalysisDisplay: 0
  inspOverrideSecondaryMix: 0
  inspDetailedTimingAdjustments: 0
  doEditorConfigPreview: 0
  inspGoOriginalName: 
--- !u!114 &580689208
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 580689202}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -1489447753, guid: 21fe281538940584e84b6d5eedb3525e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  emoterName: Emoter
  queueProcessor: {fileID: 580689205}
  lipsyncEmphasisChance: 1
  lipsyncEmphasisEmotes: []
  repeaterEmotes: []
  randomEmotes: []
  emotes:
  - expData:
      name: furrow
      components:
      - name: furrowedBrow cmpnt 0
        controlType: 0
        lipsyncControlType: 0
        emoteControlType: 0
        eyesControlType: 1
        durationDelay: 0
        durationOn: 0.3
        durationHold: 0.1
        durationOff: 0.2
        isSmoothDisable: 0
        isPersistent: 0
        expressionType: 1
        easing: 4
        expressionHandler: 1
        isAnimatorControlled: 0
        useOffset: 0
        useOffsetFollow: 1
        inspFoldout: 0
        enabled: 1
        isBonePreviewUpdated: 0
        frac: 1
        directionType: 0
      controllerVars:
      - bone: {fileID: 0}
        baseTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        startTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        endTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        fracPos: 1
        fracRot: 1
        fracScl: 1
        inspIsSetStart: 0
        inspIsSetEnd: 0
        umaUepProxy: {fileID: 0}
        uepAmount: 0
        smr: {fileID: 580689209}
        blendIndex: 12
        minShape: 0
        maxShape: 1
        eyeGizmo: {fileID: 0}
        eventSender: {fileID: 0}
        animator: {fileID: 0}
        isTriggerParameterBiDirectional: 0
        eventIdentityName: 
        onState: 2
        display2dImage: 0
        isRestNull: 0
        spriteRenderer: {fileID: 0}
        sprites: []
        uguiRenderer: {fileID: 0}
        textureRenderer: {fileID: 0}
        materialIndex: 0
        backupTextures: []
        textures: []
        materialRenderer: {fileID: 0}
        materials: []
      inspFoldout: 0
      previewDisplayMode: 0
      collectionExpand: 0
    isRandomEmote: 0
    isLipsyncEmphasisEmote: 1
    isRepeaterEmote: 0
    repeaterDelay: 0
    startDelay: 0
    isPersistent: 0
    isHoldVariationExempt: 0
    isAlwaysEmphasisEmote: 0
    hasBeenFired: 0
    frac: 1
  - expData:
      name: brows up
      components:
      - name: browsUpLeft cmpnt 0
        controlType: 0
        lipsyncControlType: 0
        emoteControlType: 0
        eyesControlType: 1
        durationDelay: 0
        durationOn: 0.3
        durationHold: 0.1
        durationOff: 0.2
        isSmoothDisable: 0
        isPersistent: 0
        expressionType: 1
        easing: 4
        expressionHandler: 1
        isAnimatorControlled: 0
        useOffset: 0
        useOffsetFollow: 1
        inspFoldout: 0
        enabled: 1
        isBonePreviewUpdated: 0
        frac: 1
        directionType: 0
      - name: browsUpRight cmpnt 1
        controlType: 0
        lipsyncControlType: 0
        emoteControlType: 0
        eyesControlType: 1
        durationDelay: 0
        durationOn: 0.3
        durationHold: 0.1
        durationOff: 0.2
        isSmoothDisable: 0
        isPersistent: 0
        expressionType: 0
        easing: 4
        expressionHandler: 0
        isAnimatorControlled: 0
        useOffset: 0
        useOffsetFollow: 1
        inspFoldout: 0
        enabled: 1
        isBonePreviewUpdated: 0
        frac: 1
        directionType: 0
      controllerVars:
      - bone: {fileID: 0}
        baseTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        startTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        endTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        fracPos: 1
        fracRot: 1
        fracScl: 1
        inspIsSetStart: 0
        inspIsSetEnd: 0
        umaUepProxy: {fileID: 0}
        uepAmount: 0
        smr: {fileID: 580689209}
        blendIndex: 9
        minShape: 0
        maxShape: 1
        eyeGizmo: {fileID: 0}
        eventSender: {fileID: 0}
        animator: {fileID: 0}
        isTriggerParameterBiDirectional: 0
        eventIdentityName: 
        onState: 2
        display2dImage: 0
        isRestNull: 0
        spriteRenderer: {fileID: 0}
        sprites: []
        uguiRenderer: {fileID: 0}
        textureRenderer: {fileID: 0}
        materialIndex: 0
        backupTextures: []
        textures: []
        materialRenderer: {fileID: 0}
        materials: []
      - bone: {fileID: 0}
        baseTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        startTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        endTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        fracPos: 1
        fracRot: 1
        fracScl: 1
        inspIsSetStart: 0
        inspIsSetEnd: 0
        umaUepProxy: {fileID: 0}
        uepAmount: 0
        smr: {fileID: 580689209}
        blendIndex: 10
        minShape: 0
        maxShape: 1
        eyeGizmo: {fileID: 0}
        eventSender: {fileID: 0}
        animator: {fileID: 0}
        isTriggerParameterBiDirectional: 0
        eventIdentityName: 
        onState: 2
        display2dImage: 0
        isRestNull: 0
        spriteRenderer: {fileID: 0}
        sprites: []
        uguiRenderer: {fileID: 0}
        textureRenderer: {fileID: 0}
        materialIndex: 0
        backupTextures: []
        textures: []
        materialRenderer: {fileID: 0}
        materials: []
      inspFoldout: 0
      previewDisplayMode: 0
      collectionExpand: 0
    isRandomEmote: 0
    isLipsyncEmphasisEmote: 0
    isRepeaterEmote: 0
    repeaterDelay: 0
    startDelay: 0
    isPersistent: 0
    isHoldVariationExempt: 0
    isAlwaysEmphasisEmote: 0
    hasBeenFired: 0
    frac: 1
  - expData:
      name: brow up R
      components:
      - name: browsUpRight cmpnt 0
        controlType: 0
        lipsyncControlType: 0
        emoteControlType: 0
        eyesControlType: 1
        durationDelay: 0
        durationOn: 0.3
        durationHold: 0.1
        durationOff: 0.2
        isSmoothDisable: 0
        isPersistent: 0
        expressionType: 1
        easing: 4
        expressionHandler: 1
        isAnimatorControlled: 0
        useOffset: 0
        useOffsetFollow: 1
        inspFoldout: 0
        enabled: 1
        isBonePreviewUpdated: 0
        frac: 1
        directionType: 0
      controllerVars:
      - bone: {fileID: 0}
        baseTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        startTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        endTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        fracPos: 1
        fracRot: 1
        fracScl: 1
        inspIsSetStart: 0
        inspIsSetEnd: 0
        umaUepProxy: {fileID: 0}
        uepAmount: 0
        smr: {fileID: 580689209}
        blendIndex: 10
        minShape: 0
        maxShape: 1
        eyeGizmo: {fileID: 0}
        eventSender: {fileID: 0}
        animator: {fileID: 0}
        isTriggerParameterBiDirectional: 0
        eventIdentityName: 
        onState: 2
        display2dImage: 0
        isRestNull: 0
        spriteRenderer: {fileID: 0}
        sprites: []
        uguiRenderer: {fileID: 0}
        textureRenderer: {fileID: 0}
        materialIndex: 0
        backupTextures: []
        textures: []
        materialRenderer: {fileID: 0}
        materials: []
      inspFoldout: 0
      previewDisplayMode: 0
      collectionExpand: 0
    isRandomEmote: 0
    isLipsyncEmphasisEmote: 1
    isRepeaterEmote: 0
    repeaterDelay: 0
    startDelay: 0
    isPersistent: 0
    isHoldVariationExempt: 0
    isAlwaysEmphasisEmote: 0
    hasBeenFired: 0
    frac: 0.37
  - expData:
      name: brow up L
      components:
      - name: browsUpLeft cmpnt 0
        controlType: 0
        lipsyncControlType: 0
        emoteControlType: 0
        eyesControlType: 1
        durationDelay: 0
        durationOn: 0.3
        durationHold: 0.1
        durationOff: 0.2
        isSmoothDisable: 0
        isPersistent: 0
        expressionType: 1
        easing: 4
        expressionHandler: 1
        isAnimatorControlled: 0
        useOffset: 0
        useOffsetFollow: 1
        inspFoldout: 0
        enabled: 1
        isBonePreviewUpdated: 0
        frac: 1
        directionType: 0
      controllerVars:
      - bone: {fileID: 0}
        baseTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        startTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        endTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        fracPos: 1
        fracRot: 1
        fracScl: 1
        inspIsSetStart: 0
        inspIsSetEnd: 0
        umaUepProxy: {fileID: 0}
        uepAmount: 0
        smr: {fileID: 580689209}
        blendIndex: 9
        minShape: 0
        maxShape: 1
        eyeGizmo: {fileID: 0}
        eventSender: {fileID: 0}
        animator: {fileID: 0}
        isTriggerParameterBiDirectional: 0
        eventIdentityName: 
        onState: 2
        display2dImage: 0
        isRestNull: 0
        spriteRenderer: {fileID: 0}
        sprites: []
        uguiRenderer: {fileID: 0}
        textureRenderer: {fileID: 0}
        materialIndex: 0
        backupTextures: []
        textures: []
        materialRenderer: {fileID: 0}
        materials: []
      inspFoldout: 0
      previewDisplayMode: 0
      collectionExpand: 0
    isRandomEmote: 0
    isLipsyncEmphasisEmote: 1
    isRepeaterEmote: 0
    repeaterDelay: 0
    startDelay: 0
    isPersistent: 0
    isHoldVariationExempt: 0
    isAlwaysEmphasisEmote: 0
    hasBeenFired: 0
    frac: 0.37
  - expData:
      name: eyes wide
      components:
      - name: eyesWide cmpnt 0
        controlType: 0
        lipsyncControlType: 0
        emoteControlType: 0
        eyesControlType: 1
        durationDelay: 0
        durationOn: 0.3
        durationHold: 0.1
        durationOff: 0.2
        isSmoothDisable: 0
        isPersistent: 0
        expressionType: 1
        easing: 4
        expressionHandler: 1
        isAnimatorControlled: 0
        useOffset: 0
        useOffsetFollow: 1
        inspFoldout: 0
        enabled: 1
        isBonePreviewUpdated: 0
        frac: 1
        directionType: 0
      controllerVars:
      - bone: {fileID: 0}
        baseTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        startTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        endTform:
          pos: {x: 0, y: 0, z: 0}
          rot: {x: 0, y: 0, z: 0, w: 0}
          scale: {x: 0, y: 0, z: 0}
        fracPos: 1
        fracRot: 1
        fracScl: 1
        inspIsSetStart: 0
        inspIsSetEnd: 0
        umaUepProxy: {fileID: 0}
        uepAmount: 0
        smr: {fileID: 580689209}
        blendIndex: 11
        minShape: 0
        maxShape: 1
        eyeGizmo: {fileID: 0}
        eventSender: {fileID: 0}
        animator: {fileID: 0}
        isTriggerParameterBiDirectional: 0
        eventIdentityName: 
        onState: 2
        display2dImage: 0
        isRestNull: 0
        spriteRenderer: {fileID: 0}
        sprites: []
        uguiRenderer: {fileID: 0}
        textureRenderer: {fileID: 0}
        materialIndex: 0
        backupTextures: []
        textures: []
        materialRenderer: {fileID: 0}
        materials: []
      inspFoldout: 0
      previewDisplayMode: 0
      collectionExpand: 0
    isRandomEmote: 0
    isLipsyncEmphasisEmote: 1
    isRepeaterEmote: 0
    repeaterDelay: 0
    startDelay: 0
    isPersistent: 0
    isHoldVariationExempt: 0
    isAlwaysEmphasisEmote: 0
    hasBeenFired: 0
    frac: 1
  useRandomEmotes: 0
  isChancePerEmote: 1
  numRandomEmotesPerCycle: 0
  numRandomEmphasizersPerCycle: 0
  randomEmoteMinTimer: 1
  randomEmoteMaxTimer: 2
  randomChance: 0.5
  useRandomFrac: 0
  randomFracBias: 0.5
  useRandomHoldDuration: 0
  randomHoldDurationMin: 0.1
  randomHoldDurationMax: 0.5
  configReady: 1
  warnOnNullRefs: 1
  inspSalsaLinked: 1
  inspStateFoldoutProcessing: 0
  inspStateFoldoutSettings: 0
  inspStateFoldoutEmotes: 0
  inspCollectionDisplayMode: 0
--- !u!137 &580689209 stripped
SkinnedMeshRenderer:
  m_CorrespondingSourceObject: {fileID: 13700000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
  m_PrefabInstance: {fileID: 1958638727}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &580689210
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 580689202}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 710820541, guid: 21fe281538940584e84b6d5eedb3525e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  salsaInstance: {fileID: 580689207}
  bufferSize: 512
  silenceThreshold: 0.9
  timingStartPoint: 0.4
  timingEndVariance: 0.8
  silenceSampleWeight: 0.9
  silenceHits: 0
--- !u!1 &597265929
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 597265933}
  - component: {fileID: 597265932}
  - component: {fileID: 597265931}
  - component: {fileID: 597265930}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &597265930
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 597265929}
  m_Enabled: 1
--- !u!124 &597265931
Behaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 597265929}
  m_Enabled: 1
--- !u!20 &597265932
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 597265929}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 2
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &597265933
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 597265929}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.13, z: -3.1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &613122998
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 613122999}
  m_Layer: 0
  m_Name: boxHead.v2_EyeCenterGizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &613122999
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 613122998}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: 1.037}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 580689203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &751161431
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 751161432}
  m_Layer: 0
  m_Name: boxHead.v2_HeadCenterGizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &751161432
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 751161431}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 119433647}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &785795839
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 785795840}
  m_Layer: 0
  m_Name: boxHead.v2_boxHead.v2_Gizmo_eyeRefFwd_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &785795840
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 785795839}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: 1.037}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 580689203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &803760220
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 803760221}
  m_Layer: 0
  m_Name: boxHead.v2_boxHead.v2_Gizmo_eyeRefFix_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &803760221
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 803760220}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: 1.037}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 580689203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &846113220
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 846113221}
  m_Layer: 0
  m_Name: boxHead.v2_boxHead.v2_Gizmo_eyeRefFix_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &846113221
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 846113220}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: 1.037}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 580689203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &979371680
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 979371681}
  m_Layer: 0
  m_Name: boxHead.v2_boxHead.v2_Gizmo_eyeRefFwd_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &979371681
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 979371680}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: 1.037}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 580689203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &989196202
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 989196203}
  m_Layer: 0
  m_Name: boxHead.v2_boxHead.v2_Gizmo_eyeRefFix_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &989196203
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 989196202}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: 1.037}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 580689203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1004491942
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1004491943}
  m_Layer: 0
  m_Name: boxHead.v2_EyeTarget
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1004491943
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1004491942}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: -4.0369997}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1011782748
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1011782749}
  m_Layer: 0
  m_Name: boxHead.v2_HeadCenterGizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1011782749
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1011782748}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 119433647}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1123668609
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1123668610}
  m_Layer: 0
  m_Name: boxHead.v2_EyeTarget
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1123668610
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1123668609}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: -4.0369997}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1206537975
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1206537976}
  m_Layer: 0
  m_Name: boxHead.v2_boxHead.v2_headRefFwd_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1206537976
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1206537975}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 119433647}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1281819598
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1281819599}
  m_Layer: 0
  m_Name: boxHead.v2_HeadTarget
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1281819599
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1281819598}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.4, z: -3}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1446234040
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1446234041}
  m_Layer: 0
  m_Name: boxHead.v2_boxHead.v2_Gizmo_eyeRefFwd_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1446234041
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1446234040}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: 1.037}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 580689203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1449024890
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1449024891}
  m_Layer: 0
  m_Name: boxHead.v2_boxHead.v2_Gizmo_eyeRefFix_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1449024891
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1449024890}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: 1.037}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 580689203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1512492696
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1512492697}
  m_Layer: 0
  m_Name: boxHead.v2_boxHead.v2_Gizmo_eyeRefFix_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1512492697
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1512492696}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: 1.037}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 580689203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1678223853
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1678223854}
  m_Layer: 0
  m_Name: boxHead.v2_boxHead.v2_Gizmo_eyeRefFwd_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1678223854
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1678223853}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: 1.037}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 580689203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1705847269
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1705847270}
  m_Layer: 0
  m_Name: boxHead.v2_boxHead.v2_Gizmo_eyeRefFix_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1705847270
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1705847269}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: 1.037}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 580689203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1709113803
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1709113804}
  m_Layer: 0
  m_Name: boxHead.v2_boxHead.v2_Gizmo_eyeRefFwd_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1709113804
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1709113803}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: 1.037}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 580689203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1817026200
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1817026201}
  m_Layer: 0
  m_Name: boxHead.v2_boxHead.v2_headRefFix_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1817026201
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1817026200}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 119433647}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1893005324
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1893005325}
  m_Layer: 0
  m_Name: boxHead.v2_EyeCenterGizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1893005325
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1893005324}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: 1.037}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 580689203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1958638727
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 119433647}
    m_Modifications:
    - target: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 180
      objectReference: {fileID: 0}
    - target: {fileID: 13700000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      propertyPath: m_DirtyAABB
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 13700000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      propertyPath: m_AABB.m_Center.y
      value: 1.1868649
      objectReference: {fileID: 0}
    - target: {fileID: 13700000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      propertyPath: m_AABB.m_Center.z
      value: 0.2588728
      objectReference: {fileID: 0}
    - target: {fileID: 13700000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      propertyPath: m_AABB.m_Extent.x
      value: 1.0000002
      objectReference: {fileID: 0}
    - target: {fileID: 13700000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      propertyPath: m_AABB.m_Extent.y
      value: 1.1868649
      objectReference: {fileID: 0}
    - target: {fileID: 13700000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      propertyPath: m_AABB.m_Extent.z
      value: 1.2588727
      objectReference: {fileID: 0}
    - target: {fileID: 13700000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      propertyPath: m_BlendShapeWeights.Array.size
      value: 9
      objectReference: {fileID: 0}
    - target: {fileID: 13700000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      propertyPath: 'm_BlendShapeWeights.Array.data[1]'
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 13700000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      propertyPath: 'm_BlendShapeWeights.Array.data[2]'
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 13700000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      propertyPath: 'm_BlendShapeWeights.Array.data[8]'
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 613122999}
    - targetCorrespondingSourceObject: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 10140499}
    - targetCorrespondingSourceObject: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 989196203}
    - targetCorrespondingSourceObject: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1446234041}
    - targetCorrespondingSourceObject: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1449024891}
    - targetCorrespondingSourceObject: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 979371681}
    - targetCorrespondingSourceObject: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 803760221}
    - targetCorrespondingSourceObject: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 310533621}
    - targetCorrespondingSourceObject: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1512492697}
    - targetCorrespondingSourceObject: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 224134274}
    - targetCorrespondingSourceObject: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1893005325}
    - targetCorrespondingSourceObject: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1709113804}
    - targetCorrespondingSourceObject: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 846113221}
    - targetCorrespondingSourceObject: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 574139586}
    - targetCorrespondingSourceObject: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1705847270}
    - targetCorrespondingSourceObject: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 785795840}
    - targetCorrespondingSourceObject: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 146297975}
    - targetCorrespondingSourceObject: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1678223854}
    - targetCorrespondingSourceObject: {fileID: 400000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 2083864910}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 100000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 580689206}
    - targetCorrespondingSourceObject: {fileID: 100000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 580689204}
    - targetCorrespondingSourceObject: {fileID: 100000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 580689205}
    - targetCorrespondingSourceObject: {fileID: 100000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 580689207}
    - targetCorrespondingSourceObject: {fileID: 100000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 580689208}
    - targetCorrespondingSourceObject: {fileID: 100000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
      insertIndex: -1
      addedObject: {fileID: 580689210}
  m_SourcePrefab: {fileID: 100100000, guid: 2ca10c22b135faf419bbb2d6cba14789, type: 3}
--- !u!1 &2035328004
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2035328005}
  m_Layer: 0
  m_Name: boxHead.v2_boxHead.v2_headRefFwd_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2035328005
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2035328004}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 119433647}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2083864909
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2083864910}
  m_Layer: 0
  m_Name: boxHead.v2_boxHead.v2_Gizmo_eyeRefFix_Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2083864910
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2083864909}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.378, z: 1.037}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 580689203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2121294015
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2121294016}
  m_Layer: 0
  m_Name: boxHead.v2_HeadTarget
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2121294016
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2121294015}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.4, z: -3}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 597265933}
  - {fileID: 519288686}
  - {fileID: 119433647}
  - {fileID: 1281819599}
  - {fileID: 1123668610}
  - {fileID: 2121294016}
  - {fileID: 1004491943}
