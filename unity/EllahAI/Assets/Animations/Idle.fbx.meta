fileFormatVersion: 2
guid: 9a309ce6ca63b4d1db7d70ef39a8a88e
ModelImporter:
  serializedVersion: 22200
  internalIDToNameTable: []
  externalObjects: {}
  materials:
    materialImportMode: 2
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 1
    removeConstantScaleCurves: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: mixamo.com
      takeName: mixamo.com
      internalID: -203655887218126122
      firstFrame: 0
      lastFrame: 500
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importPhysicalCameras: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: mixamorig:Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:Spine1
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftToeBase
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightToeBase
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandThumb1
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandThumb2
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandThumb3
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandIndex1
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandIndex2
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandIndex3
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandMiddle1
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandMiddle2
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandMiddle3
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandRing1
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandRing2
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandRing3
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandPinky1
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandPinky2
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandPinky3
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandThumb1
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandThumb2
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandThumb3
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandIndex1
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandIndex2
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandIndex3
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandMiddle1
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandMiddle2
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandMiddle3
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandRing1
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandRing2
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandRing3
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandPinky1
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandPinky2
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandPinky3
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:Spine2
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Idle(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Hips
      parentName: Idle(Clone)
      position: {x: -0.004888531, y: 1.0447373, z: 0.015563922}
      rotation: {x: -0.0019834565, y: -0.013091596, z: 0.047286205, w: 0.99879366}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Spine
      parentName: mixamorig:Hips
      position: {x: -0, y: 0.10182399, z: 0}
      rotation: {x: -0.06910204, y: 0.012513869, z: -0.0257681, w: 0.9971982}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Spine1
      parentName: mixamorig:Spine
      position: {x: -0, y: 0.100026995, z: 0}
      rotation: {x: 0.0064704283, y: 0.0056749173, z: -0.051593702, w: 0.9986311}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Spine2
      parentName: mixamorig:Spine1
      position: {x: -0, y: 0.093220994, z: 0}
      rotation: {x: 0.019410135, y: 0.00042419942, z: -0.051495567, w: 0.9984845}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Neck
      parentName: mixamorig:Spine2
      position: {x: -0, y: 0.16865298, z: 0}
      rotation: {x: 0.009170465, y: 0.00017066383, z: 0.019068621, w: 0.9997761}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Head
      parentName: mixamorig:Neck
      position: {x: -0, y: 0.09341899, z: 0.02841}
      rotation: {x: 0.013458861, y: -0.0142904585, z: 0.06968658, w: 0.9973758}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:HeadTop_End
      parentName: mixamorig:Head
      position: {x: -0, y: 0.20962799, z: 0.101229}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightShoulder
      parentName: mixamorig:Spine2
      position: {x: 0.045700002, y: 0.111958, z: -0.0080659995}
      rotation: {x: 0.5402225, y: 0.5574842, z: -0.5124064, w: 0.3671657}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightArm
      parentName: mixamorig:RightShoulder
      position: {x: -0, y: 0.108381994, z: 0}
      rotation: {x: -0.1449792, y: 0.012183157, z: 0.046647687, w: 0.98825943}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightForeArm
      parentName: mixamorig:RightArm
      position: {x: -0, y: 0.278415, z: 0}
      rotation: {x: -0.0059379027, y: -0.00092664803, z: 0.03488927, w: 0.9993732}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHand
      parentName: mixamorig:RightForeArm
      position: {x: -0, y: 0.283288, z: 0}
      rotation: {x: 0.08640048, y: -0.10459958, z: -0.07080995, w: 0.9882206}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandThumb1
      parentName: mixamorig:RightHand
      position: {x: -0.026819, y: 0.024647998, z: 0.01574}
      rotation: {x: 0.051459115, y: 0.055571694, z: 0.33826065, w: 0.9379997}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandThumb2
      parentName: mixamorig:RightHandThumb1
      position: {x: -0, y: 0.04189, z: 0}
      rotation: {x: -0.0051530614, y: -0.13704348, z: -0.03037347, w: 0.9900859}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandThumb3
      parentName: mixamorig:RightHandThumb2
      position: {x: -0, y: 0.034163, z: 0}
      rotation: {x: -0.0013036872, y: -0.02295327, z: -0.03998869, w: 0.9989357}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandThumb4
      parentName: mixamorig:RightHandThumb3
      position: {x: -0, y: 0.02575, z: 0}
      rotation: {x: 0.005774398, y: 0.11750944, z: 0.048743945, w: 0.991858}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandIndex1
      parentName: mixamorig:RightHand
      position: {x: -0.022597998, y: 0.091083, z: 0.0051789996}
      rotation: {x: 0.06822295, y: 0.00016623695, z: -0.040861286, w: 0.99683297}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandIndex2
      parentName: mixamorig:RightHandIndex1
      position: {x: -0, y: 0.037, z: 0}
      rotation: {x: 0.034148823, y: -0.0010930488, z: -0.0047562914, w: 0.99940485}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandIndex3
      parentName: mixamorig:RightHandIndex2
      position: {x: -0, y: 0.028499998, z: 0}
      rotation: {x: 0.037553944, y: -0.0019134157, z: -0.0005215432, w: 0.9992926}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandIndex4
      parentName: mixamorig:RightHandIndex3
      position: {x: -0, y: 0.027722001, z: 0}
      rotation: {x: -0.00000008750134, y: -0.0010026915, z: -0.00008726642, w: 0.9999995}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandMiddle1
      parentName: mixamorig:RightHand
      position: {x: -0, y: 0.095325, z: 0}
      rotation: {x: 0.06914302, y: -0.0026887408, z: -0.03885075, w: 0.9968463}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandMiddle2
      parentName: mixamorig:RightHandMiddle1
      position: {x: -0, y: 0.037, z: 0}
      rotation: {x: 0.028447082, y: -0.0024318697, z: -0.0035361978, w: 0.9995861}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandMiddle3
      parentName: mixamorig:RightHandMiddle2
      position: {x: -0, y: 0.0295, z: 0}
      rotation: {x: 0.036394093, y: -0.00016907873, z: -0.003910846, w: 0.99932986}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandMiddle4
      parentName: mixamorig:RightHandMiddle3
      position: {x: -0, y: 0.029466, z: 0}
      rotation: {x: -0.00000015476391, y: -0.00092851504, z: -0.00016667887, w: 0.9999996}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandRing1
      parentName: mixamorig:RightHand
      position: {x: 0.018651, y: 0.09103599, z: 0.000431}
      rotation: {x: 0.06760176, y: -0.0021408785, z: -0.030684661, w: 0.9972381}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandRing2
      parentName: mixamorig:RightHandRing1
      position: {x: -0, y: 0.033793, z: 0}
      rotation: {x: 0.02586296, y: 0.002505747, z: -0.0027875642, w: 0.99965847}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandRing3
      parentName: mixamorig:RightHandRing2
      position: {x: -0, y: 0.028896999, z: 0}
      rotation: {x: 0.035795607, y: -0.0013294037, z: 0.00011865253, w: 0.99935824}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandRing4
      parentName: mixamorig:RightHandRing3
      position: {x: -0, y: 0.026387999, z: 0}
      rotation: {x: 0.00000011649483, y: -0.00014573496, z: 0.00079936074, w: 0.9999997}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandPinky1
      parentName: mixamorig:RightHand
      position: {x: 0.038062997, y: 0.080767, z: 0.004867}
      rotation: {x: 0.06295933, y: 0.0016609046, z: -0.030225992, w: 0.99755687}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandPinky2
      parentName: mixamorig:RightHandPinky1
      position: {x: -0, y: 0.036, z: 0}
      rotation: {x: 0.023543252, y: -0.0029479472, z: -0.00227691, w: 0.9997159}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandPinky3
      parentName: mixamorig:RightHandPinky2
      position: {x: -0, y: 0.020999998, z: 0}
      rotation: {x: 0.0338625, y: -0.006769057, z: -0.00016048818, w: 0.99940366}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandPinky4
      parentName: mixamorig:RightHandPinky3
      position: {x: -0, y: 0.021157999, z: 0}
      rotation: {x: 0.00000006435498, y: -0.0015690505, z: 0.00004101519, w: 0.9999988}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftShoulder
      parentName: mixamorig:Spine2
      position: {x: -0.045704, y: 0.11195599, z: -0.0080659995}
      rotation: {x: 0.5885932, y: -0.5102588, z: 0.4714892, w: 0.41339105}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftArm
      parentName: mixamorig:LeftShoulder
      position: {x: -0, y: 0.108376995, z: 0}
      rotation: {x: 0.021894965, y: -0.087322086, z: -0.008486923, w: 0.9959034}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftForeArm
      parentName: mixamorig:LeftArm
      position: {x: -0, y: 0.278415, z: 0}
      rotation: {x: -0.006562161, y: 0.00083515764, z: -0.035904802, w: 0.9993334}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHand
      parentName: mixamorig:LeftForeArm
      position: {x: -0, y: 0.283288, z: 0}
      rotation: {x: 0.0835638, y: 0.24025173, z: 0.011875258, w: 0.9670343}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandThumb1
      parentName: mixamorig:LeftHand
      position: {x: 0.026817, y: 0.024660999, z: 0.015762}
      rotation: {x: 0.09684888, y: -0.06775118, z: -0.32624948, w: 0.9378654}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandThumb2
      parentName: mixamorig:LeftHandThumb1
      position: {x: -0, y: 0.041871, z: 0}
      rotation: {x: -0.0051475056, y: 0.13717027, z: 0.030373331, w: 0.9900683}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandThumb3
      parentName: mixamorig:LeftHandThumb2
      position: {x: -0, y: 0.034184, z: 0}
      rotation: {x: -0.0047404026, y: 0.022287173, z: 0.0393365, w: 0.9989663}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandThumb4
      parentName: mixamorig:LeftHandThumb3
      position: {x: -0, y: 0.025806, z: 0}
      rotation: {x: 0.005153017, y: -0.12268283, z: -0.04165188, w: 0.99155813}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandIndex1
      parentName: mixamorig:LeftHand
      position: {x: 0.022599, y: 0.091093, z: 0.00518}
      rotation: {x: 0.06822293, y: -0.00016617293, z: 0.040860813, w: 0.996833}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandIndex2
      parentName: mixamorig:LeftHandIndex1
      position: {x: -0, y: 0.037, z: 0}
      rotation: {x: 0.034147967, y: 0.0010932524, z: 0.0047559654, w: 0.99940497}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandIndex3
      parentName: mixamorig:LeftHandIndex2
      position: {x: -0, y: 0.028499998, z: 0}
      rotation: {x: 0.03904364, y: 0.00033199915, z: 0.0031395103, w: 0.99923253}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandIndex4
      parentName: mixamorig:LeftHandIndex3
      position: {x: -0, y: 0.027748998, z: 0}
      rotation: {x: -0.000000004946225, y: 0.00037786376, z: 0.000013089969, w: 0.99999994}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandMiddle1
      parentName: mixamorig:LeftHand
      position: {x: -0, y: 0.09533399, z: 0}
      rotation: {x: 0.06914589, y: 0.0026888233, z: 0.03885166, w: 0.9968462}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandMiddle2
      parentName: mixamorig:LeftHandMiddle1
      position: {x: -0, y: 0.037, z: 0}
      rotation: {x: 0.028442027, y: 0.0024321228, z: 0.0035347508, w: 0.9995862}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandMiddle3
      parentName: mixamorig:LeftHandMiddle2
      position: {x: -0, y: 0.0295, z: 0}
      rotation: {x: 0.03793902, y: -0.0000924772, z: 0.0046455422, w: 0.9992693}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandMiddle4
      parentName: mixamorig:LeftHandMiddle3
      position: {x: -0, y: 0.029528998, z: 0}
      rotation: {x: 0.000000028609662, y: 0.001024508, z: -0.000027925253, w: 0.99999946}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandRing1
      parentName: mixamorig:LeftHand
      position: {x: -0.018651, y: 0.091045, z: 0.00043000001}
      rotation: {x: 0.067600645, y: 0.0021413995, z: 0.030684367, w: 0.9972382}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandRing2
      parentName: mixamorig:LeftHandRing1
      position: {x: -0, y: 0.0315, z: 0}
      rotation: {x: 0.025862664, y: -0.0025051283, z: 0.0027869, w: 0.9996585}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandRing3
      parentName: mixamorig:LeftHandRing2
      position: {x: -0, y: 0.0295, z: 0}
      rotation: {x: 0.034901395, y: -0.0020238445, z: 0.0034470034, w: 0.99938285}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandRing4
      parentName: mixamorig:LeftHandRing3
      position: {x: -0, y: 0.026442999, z: 0}
      rotation: {x: -0.000000007744897, y: -0.0004930555, z: -0.000015707961, w: 0.9999999}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandPinky1
      parentName: mixamorig:LeftHand
      position: {x: -0.038062997, y: 0.080777995, z: 0.004869}
      rotation: {x: 0.06296017, y: -0.0016603462, z: 0.030226167, w: 0.99755687}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandPinky2
      parentName: mixamorig:LeftHandPinky1
      position: {x: -0, y: 0.036, z: 0}
      rotation: {x: 0.023540303, y: 0.0029486045, z: 0.0022765878, w: 0.999716}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandPinky3
      parentName: mixamorig:LeftHandPinky2
      position: {x: -0, y: 0.020999998, z: 0}
      rotation: {x: 0.034149837, y: 0.0002629513, z: 0.004845679, w: 0.999405}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandPinky4
      parentName: mixamorig:LeftHandPinky3
      position: {x: -0, y: 0.021255, z: 0}
      rotation: {x: 0.00000064151766, y: 0.00078365306, z: 0.0002949599, w: 0.9999997}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightUpLeg
      parentName: mixamorig:Hips
      position: {x: 0.082077995, y: -0.067718, z: -0.015121999}
      rotation: {x: 0.04142134, y: 0.024860099, z: 0.99429864, w: 0.09506042}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightLeg
      parentName: mixamorig:RightUpLeg
      position: {x: -0, y: 0.44371498, z: 0}
      rotation: {x: -0.14083713, y: -0.04641711, z: -0.02510135, w: 0.98862547}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightFoot
      parentName: mixamorig:RightLeg
      position: {x: -0, y: 0.44527802, z: 0}
      rotation: {x: 0.53315204, y: 0.030622909, z: 0.0631834, w: 0.84310085}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightToeBase
      parentName: mixamorig:RightFoot
      position: {x: -0, y: 0.138169, z: 0}
      rotation: {x: 0.34005785, y: -0.000000029230446, z: 0.00000016238285, w: 0.94040453}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightToe_End
      parentName: mixamorig:RightToeBase
      position: {x: -0, y: 0.092781, z: 0}
      rotation: {x: 0, y: -0.0116079245, z: -0, w: 0.99993265}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftUpLeg
      parentName: mixamorig:Hips
      position: {x: -0.082077995, y: -0.067718, z: -0.015121999}
      rotation: {x: -0.11884873, y: 0.12470072, z: 0.97911644, w: 0.10796159}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftLeg
      parentName: mixamorig:LeftUpLeg
      position: {x: -0, y: 0.443714, z: 0}
      rotation: {x: -0.24932343, y: 0.077437304, z: -0.035045758, w: 0.96468294}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftFoot
      parentName: mixamorig:LeftLeg
      position: {x: -0, y: 0.44527802, z: 0}
      rotation: {x: 0.5349399, y: 0.0055140103, z: 0.09957299, w: 0.8389841}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftToeBase
      parentName: mixamorig:LeftFoot
      position: {x: -0, y: 0.138169, z: 0}
      rotation: {x: 0.33913115, y: -0.00000005925769, z: 0.00000017701622, w: 0.94073915}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftToe_End
      parentName: mixamorig:LeftToeBase
      position: {x: -0, y: 0.092781, z: 0}
      rotation: {x: 0, y: 0.011868834, z: -0, w: 0.9999296}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  importBlendShapeDeformPercent: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
