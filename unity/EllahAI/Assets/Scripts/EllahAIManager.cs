using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;
using System.Linq;
using FlutterUnityIntegration;

public class EllahAIManager : MonoBehaviour
{
    [Header("Avatar Components")]
    public AvatarController avatarController;
    public EnvironmentManager environmentManager;
    public AnimationController animationController;
    
    private bool isInitialized = false;
    
    void Start()
    {
        InitializeGame();
    }
    
    void InitializeGame()
    {
        try
        {
            // UnityMessageManager should already be added to this GameObject in the scene
            
            // Initialize all components
            if (avatarController == null)
                avatarController = FindObjectOfType<AvatarController>();
            
            if (environmentManager == null)
                environmentManager = FindObjectOfType<EnvironmentManager>();
            
            if (animationController == null)
                animationController = FindObjectOfType<AnimationController>();
            
            // Initialize avatar
            if (avatarController != null)
            {
                avatarController.Initialize();
            }
            
            // Initialize environment
            if (environmentManager != null)
            {
                environmentManager.Initialize();
            }
            
            isInitialized = true;
            SendMessageToFlutter("avatar_ready");
            
            Debug.Log("EllahAIManager initialized successfully");
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to initialize EllahAIManager: {e.Message}");
            SendErrorToFlutter($"Initialization failed: {e.Message}");
        }
    }
    
    // Method to receive messages from Flutter
    public void ReceiveCommand(string message)
    {
        if (!isInitialized)
        {
            Debug.LogWarning("EllahAIManager not initialized, ignoring command");
            return;
        }
        
        try
        {
            var commandData = UnityEngine.JsonUtility.FromJson<UnityCommand>(message);
            ProcessCommand(commandData);
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to process command: {e.Message}");
            SendErrorToFlutter($"Command processing failed: {e.Message}");
        }
    }
    
    // Method to handle Flutter messages (required by UnityMessageManager)
    public void OnFlutterMessage(string message)
    {
        ReceiveCommand(message);
    }
    
    void ProcessCommand(UnityCommand commandData)
    {
        Debug.Log($"Processing command: {commandData.command}");
        
        switch (commandData.command)
        {
            case "startTalking":
                HandleStartTalking(commandData.parameters);
                break;
                
            case "stopTalking":
                HandleStopTalking();
                break;
                
            case "changeExpression":
                HandleChangeExpression(commandData.parameters);
                break;
                
            case "changeEnvironment":
                HandleChangeEnvironment(commandData.parameters);
                break;
                
            case "changeOutfit":
                HandleChangeOutfit(commandData.parameters);
                break;
                
            case "playGesture":
                HandlePlayGesture(commandData.parameters);
                break;
                
            case "playAnimation":
                HandlePlayAnimation(commandData.parameters);
                break;
                
            case "initializeAvatar":
                HandleInitializeAvatar(commandData.parameters);
                break;
                
            case "cleanup":
                HandleCleanup();
                break;

            // Camera commands
            case "setCameraMode":
                HandleSetCameraMode(commandData.parameters);
                break;

            case "setCameraPosition":
                HandleSetCameraPosition(commandData.parameters);
                break;

            case "setCameraZoom":
                HandleSetCameraZoom(commandData.parameters);
                break;

            case "smoothCameraTransition":
                HandleSmoothCameraTransition(commandData.parameters);
                break;

            // Cosmetic commands
            case "changeAccessory":
                HandleChangeAccessory(commandData.parameters);
                break;

            case "removeAccessory":
                HandleRemoveAccessory(commandData.parameters);
                break;

            case "previewCosmetic":
                HandlePreviewCosmetic(commandData.parameters);
                break;

            case "applyCosmetic":
                HandleApplyCosmetic(commandData.parameters);
                break;

            case "revertCosmetics":
                HandleRevertCosmetics();
                break;

            // Utility commands
            case "ping":
                HandlePing(commandData.parameters);
                break;

            case "getState":
                HandleGetState();
                break;

            case "batchCommands":
                HandleBatchCommands(commandData.parameters);
                break;

            case "setDebugMode":
                HandleSetDebugMode(commandData.parameters);
                break;

            case "setLighting":
                HandleSetLighting(commandData.parameters);
                break;

            case "setCameraAngle":
                HandleSetCameraAngle(commandData.parameters);
                break;

            default:
                Debug.LogWarning($"Unknown command: {commandData.command}");
                SendErrorToFlutter($"Unknown command: {commandData.command}");
                break;
        }
    }
    
    void HandleStartTalking(Dictionary<string, object> parameters)
    {
        bool isUser = parameters.ContainsKey("isUser") ? (bool)parameters["isUser"] : false;
        string emotion = parameters.ContainsKey("emotion") ? (string)parameters["emotion"] : "neutral";
        
        if (avatarController != null)
        {
            avatarController.StartTalking(isUser, emotion);
        }
        
        if (animationController != null)
        {
            animationController.PlayTalkingAnimation(emotion);
        }
        
        SendMessageToFlutter("talking_state", new { isTalking = true, isUser = isUser });
    }
    
    void HandleStopTalking()
    {
        if (avatarController != null)
        {
            avatarController.StopTalking();
        }
        
        if (animationController != null)
        {
            animationController.StopTalkingAnimation();
        }
        
        SendMessageToFlutter("talking_state", new { isTalking = false });
    }
    
    void HandleChangeExpression(Dictionary<string, object> parameters)
    {
        string expression = parameters.ContainsKey("expression") ? (string)parameters["expression"] : "neutral";
        
        if (avatarController != null)
        {
            avatarController.ChangeExpression(expression);
        }
        
        SendMessageToFlutter("expression_changed", new { expression = expression });
    }
    
    void HandleChangeEnvironment(Dictionary<string, object> parameters)
    {
        string environment = parameters.ContainsKey("environment") ? (string)parameters["environment"] : "cozy_room";
        
        if (environmentManager != null)
        {
            environmentManager.ChangeEnvironment(environment);
        }
        
        SendMessageToFlutter("environment_changed", new { environment = environment });
    }
    
    void HandleChangeOutfit(Dictionary<string, object> parameters)
    {
        string outfit = parameters.ContainsKey("outfit") ? (string)parameters["outfit"] : "default";
        
        if (avatarController != null)
        {
            avatarController.ChangeOutfit(outfit);
        }
        
        SendMessageToFlutter("outfit_changed", new { outfit = outfit });
    }
    
    void HandlePlayGesture(Dictionary<string, object> parameters)
    {
        string gesture = parameters.ContainsKey("gesture") ? (string)parameters["gesture"] : "wave";
        
        if (animationController != null)
        {
            animationController.PlayGesture(gesture);
        }
    }
    
    void HandlePlayAnimation(Dictionary<string, object> parameters)
    {
        string animation = parameters.ContainsKey("animation") ? (string)parameters["animation"] : "idle";
        
        if (animationController != null)
        {
            animationController.PlayAnimation(animation);
        }
    }
    
    void HandleInitializeAvatar(Dictionary<string, object> parameters)
    {
        if (avatarController != null)
        {
            avatarController.InitializeWithSettings(parameters);
        }
    }
    
    void HandleCleanup()
    {
        // Perform any necessary cleanup
        if (avatarController != null)
        {
            avatarController.Cleanup();
        }

        if (environmentManager != null)
        {
            environmentManager.Cleanup();
        }

        Debug.Log("Cleanup completed");
    }

    // Camera command handlers
    void HandleSetCameraMode(Dictionary<string, object> parameters)
    {
        string mode = parameters.ContainsKey("mode") ? (string)parameters["mode"] : "default";

        // Find camera controller or implement camera mode switching
        Camera mainCamera = Camera.main;
        if (mainCamera != null)
        {
            // Implement camera mode switching logic here
            Debug.Log($"Setting camera mode to: {mode}");
            SendMessageToFlutter("camera_mode_changed", new { mode = mode });
        }
    }

    void HandleSetCameraPosition(Dictionary<string, object> parameters)
    {
        if (parameters.ContainsKey("position") && parameters.ContainsKey("rotation"))
        {
            // Parse position and rotation from parameters
            // This would need proper Vector3 parsing implementation
            Debug.Log("Setting camera position and rotation");
            SendMessageToFlutter("camera_state_changed", new {
                position = parameters["position"],
                rotation = parameters["rotation"]
            });
        }
    }

    void HandleSetCameraZoom(Dictionary<string, object> parameters)
    {
        float zoom = parameters.ContainsKey("zoom") ? Convert.ToSingle(parameters["zoom"]) : 60f;

        Camera mainCamera = Camera.main;
        if (mainCamera != null)
        {
            mainCamera.fieldOfView = zoom;
            Debug.Log($"Setting camera zoom to: {zoom}");
            SendMessageToFlutter("camera_state_changed", new { zoom = zoom });
        }
    }

    void HandleSmoothCameraTransition(Dictionary<string, object> parameters)
    {
        float duration = parameters.ContainsKey("duration") ? Convert.ToSingle(parameters["duration"]) : 1f;

        // Implement smooth camera transition
        Debug.Log($"Starting smooth camera transition with duration: {duration}");
        SendMessageToFlutter("camera_state_changed", new { isTransitioning = true });

        // Start coroutine for smooth transition
        StartCoroutine(SmoothCameraTransitionCoroutine(duration));
    }

    IEnumerator SmoothCameraTransitionCoroutine(float duration)
    {
        yield return new WaitForSeconds(duration);
        SendMessageToFlutter("camera_transition_complete");
    }

    // Cosmetic command handlers
    void HandleChangeAccessory(Dictionary<string, object> parameters)
    {
        string accessoryType = parameters.ContainsKey("type") ? (string)parameters["type"] : "";
        string accessoryId = parameters.ContainsKey("id") ? (string)parameters["id"] : "";

        if (avatarController != null)
        {
            // Implement accessory changing logic
            Debug.Log($"Changing accessory: {accessoryType} to {accessoryId}");
            SendMessageToFlutter("cosmetic_applied", new { type = accessoryType, id = accessoryId });
        }
    }

    void HandleRemoveAccessory(Dictionary<string, object> parameters)
    {
        string accessoryType = parameters.ContainsKey("type") ? (string)parameters["type"] : "";

        if (avatarController != null)
        {
            // Implement accessory removal logic
            Debug.Log($"Removing accessory: {accessoryType}");
            SendMessageToFlutter("cosmetic_reverted", new { type = accessoryType });
        }
    }

    void HandlePreviewCosmetic(Dictionary<string, object> parameters)
    {
        string cosmeticType = parameters.ContainsKey("type") ? (string)parameters["type"] : "";
        string cosmeticId = parameters.ContainsKey("id") ? (string)parameters["id"] : "";

        if (avatarController != null)
        {
            // Implement cosmetic preview logic
            Debug.Log($"Previewing cosmetic: {cosmeticType} - {cosmeticId}");
            SendMessageToFlutter("cosmetic_applied", new { type = cosmeticType, id = cosmeticId, isPreview = true });
        }
    }

    void HandleApplyCosmetic(Dictionary<string, object> parameters)
    {
        string cosmeticType = parameters.ContainsKey("type") ? (string)parameters["type"] : "";
        string cosmeticId = parameters.ContainsKey("id") ? (string)parameters["id"] : "";

        if (avatarController != null)
        {
            // Implement cosmetic application logic
            Debug.Log($"Applying cosmetic: {cosmeticType} - {cosmeticId}");
            SendMessageToFlutter("cosmetic_applied", new { type = cosmeticType, id = cosmeticId, isPreview = false });
        }
    }

    void HandleRevertCosmetics()
    {
        if (avatarController != null)
        {
            // Implement cosmetic reversion logic
            Debug.Log("Reverting all cosmetics to default");
            SendMessageToFlutter("cosmetic_reverted", new { type = "all" });
        }
    }

    // Utility command handlers
    void HandlePing(Dictionary<string, object> parameters)
    {
        long timestamp = parameters.ContainsKey("timestamp") ? Convert.ToInt64(parameters["timestamp"]) : 0;

        Debug.Log("Received ping from Flutter");
        SendMessageToFlutter("pong", new {
            originalTimestamp = timestamp,
            unityTimestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        });
    }

    void HandleGetState()
    {
        // Get current expression safely
        string currentExpression = "neutral";
        if (avatarController != null)
        {
            // Try to get expression if method exists, otherwise use default
            try
            {
                var expressionMethod = avatarController.GetType().GetMethod("GetCurrentExpression");
                if (expressionMethod != null)
                {
                    currentExpression = (string)expressionMethod.Invoke(avatarController, null) ?? "neutral";
                }
            }
            catch
            {
                currentExpression = "neutral";
            }
        }

        // Get current environment safely
        string currentEnvironment = "default";
        if (environmentManager != null)
        {
            try
            {
                var environmentMethod = environmentManager.GetType().GetMethod("GetCurrentEnvironment");
                if (environmentMethod != null)
                {
                    currentEnvironment = (string)environmentMethod.Invoke(environmentManager, null) ?? "default";
                }
            }
            catch
            {
                currentEnvironment = "default";
            }
        }

        // Get talking state safely
        bool isTalking = false;
        if (avatarController != null)
        {
            try
            {
                var talkingMethod = avatarController.GetType().GetMethod("IsTalking");
                if (talkingMethod != null)
                {
                    isTalking = (bool)talkingMethod.Invoke(avatarController, null);
                }
            }
            catch
            {
                isTalking = false;
            }
        }

        var currentState = new
        {
            isInitialized = isInitialized,
            currentExpression = currentExpression,
            currentEnvironment = currentEnvironment,
            isTalking = isTalking,
            cameraMode = "default", // Get from camera controller
            version = "1.0.0"
        };

        Debug.Log("Sending current state to Flutter");
        SendMessageToFlutter("state_update", currentState);
    }

    void HandleBatchCommands(Dictionary<string, object> parameters)
    {
        if (parameters.ContainsKey("commands"))
        {
            // Parse and execute batch commands
            Debug.Log("Processing batch commands");

            // This would need proper implementation to parse the commands array
            // For now, just acknowledge the batch
            int commandCount = parameters.ContainsKey("count") ? Convert.ToInt32(parameters["count"]) : 0;
            SendMessageToFlutter("batch_commands_processed", new { count = commandCount });
        }
    }

    void HandleSetDebugMode(Dictionary<string, object> parameters)
    {
        bool enabled = parameters.ContainsKey("enabled") ? (bool)parameters["enabled"] : false;

        Debug.Log($"Setting debug mode: {enabled}");
        // Implement debug mode logic here

        SendMessageToFlutter("debug_mode_changed", new { enabled = enabled });
    }

    void HandleSetLighting(Dictionary<string, object> parameters)
    {
        string lightingMode = parameters.ContainsKey("mode") ? (string)parameters["mode"] : "default";

        Debug.Log($"Setting lighting mode: {lightingMode}");
        // Implement lighting change logic here

        SendMessageToFlutter("lighting_changed", new { mode = lightingMode });
    }

    void HandleSetCameraAngle(Dictionary<string, object> parameters)
    {
        string angle = parameters.ContainsKey("angle") ? (string)parameters["angle"] : "front";

        Debug.Log($"Setting camera angle: {angle}");
        // Implement camera angle change logic here

        SendMessageToFlutter("camera_angle_changed", new { angle = angle });
    }
    
    public void SendMessageToFlutter(string messageType, object data = null)
    {
        try
        {
            var message = new
            {
                type = messageType,
                data = data,
                timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            };
            
            string jsonMessage = JsonUtility.ToJson(message);
            UnityMessageManager.Instance.SendMessageToFlutter(jsonMessage);
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to send message to Flutter: {e.Message}");
        }
    }
    
    public void SendErrorToFlutter(string errorMessage)
    {
        SendMessageToFlutter("error", new { message = errorMessage });
    }
    
    void OnApplicationPause(bool pauseStatus)
    {
        if (pauseStatus)
        {
            // Pause avatar animations
            if (avatarController != null)
            {
                avatarController.Pause();
            }
        }
        else
        {
            // Resume avatar animations
            if (avatarController != null)
            {
                avatarController.Resume();
            }
        }
    }
    
    void OnApplicationFocus(bool hasFocus)
    {
        if (!hasFocus)
        {
            // Reduce performance when not focused
            if (avatarController != null)
            {
                avatarController.SetLowPerformanceMode(true);
            }
        }
        else
        {
            // Restore performance when focused
            if (avatarController != null)
            {
                avatarController.SetLowPerformanceMode(false);
            }
        }
    }
}

// CommandData class moved to UnityCommandStructures.cs
