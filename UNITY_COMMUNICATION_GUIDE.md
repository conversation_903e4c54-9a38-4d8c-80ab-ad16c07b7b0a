# Unity-Flutter Communication Guide

This guide explains how the Flutter app communicates with <PERSON> using the `flutter_unity_widget` package.

## Overview

The communication system is fully set up in the main navigation file and Unity provider. Here's how it works:

### Flutter to Unity Communication

Flutter sends commands to Unity using the `postMessage` method:

```dart
// Example: Change avatar expression
unityController.postMessage(
  'GameManager',        // Unity GameObject name
  'ReceiveCommand',     // Unity method name
  jsonMessage          // JSON string message
);
```

### Unity to Flutter Communication

Unity sends messages back to Flutter using the Unity Message Manager:

```csharp
// In Unity C# script
GetComponent<UnityMessageManager>().SendMessageToFlutter("avatar_ready");
```

## Message Format

### Flutter to Unity

All commands are sent as JSON strings with this structure:

```json
{
  "command": "changeExpression",
  "parameters": {
    "expression": "happy"
  },
  "timestamp": **********
}
```

### Unity to Flutter

Unity can send either simple strings or structured JSON:

```json
{
  "type": "expression_changed",
  "data": {
    "expression": "happy"
  }
}
```

## Available Commands (Flutter → Unity)

### Avatar Control
- `startTalking` - Start avatar talking animation
- `stopTalking` - Stop avatar talking animation
- `changeExpression` - Change facial expression
- `changeEnvironment` - Change background environment
- `changeOutfit` - Change avatar outfit

### Camera Control
- `setCameraMode` - Switch between default and preview modes
- `setCameraPosition` - Set camera position and rotation
- `setCameraZoom` - Adjust camera field of view
- `smoothCameraTransition` - Smooth camera movement

### Cosmetics
- `changeAccessory` - Apply accessory to avatar
- `removeAccessory` - Remove accessory from avatar
- `previewCosmetic` - Preview cosmetic temporarily
- `applyCosmetic` - Apply cosmetic permanently
- `revertCosmetics` - Revert to default appearance

### Utility
- `initializeAvatar` - Initialize avatar with settings
- `ping` - Test communication
- `getState` - Request current Unity state
- `batchCommands` - Send multiple commands at once
- `cleanup` - Clean up Unity resources

## Message Types (Unity → Flutter)

### Status Messages
- `avatar_ready` - Avatar is initialized and ready
- `animation_complete` - Animation finished
- `environment_changed` - Environment change completed
- `expression_changed` - Expression change completed

### Camera Messages
- `camera_transition_complete` - Camera movement finished
- `camera_mode_changed` - Camera mode switched
- `camera_state_changed` - Camera state updated

### Error Messages
- `error` - Error occurred in Unity

### Utility Messages
- `scene_loaded` - Unity scene loaded
- `unity_unloaded` - Unity widget unloaded
- `pong` - Response to ping command

## Unity Setup Requirements

### 1. GameObject Setup

Create a GameObject named `GameManager` with these components:
- Unity Message Manager (from flutter_unity_widget package)
- Your custom script that handles commands

### 2. Script Example

```csharp
using UnityEngine;
using FlutterUnityIntegration;

public class GameManager : MonoBehaviour
{
    private UnityMessageManager messageManager;
    
    void Start()
    {
        messageManager = GetComponent<UnityMessageManager>();
        
        // Send ready message to Flutter
        messageManager.SendMessageToFlutter("avatar_ready");
    }
    
    // This method receives commands from Flutter
    public void ReceiveCommand(string jsonMessage)
    {
        try
        {
            var command = JsonUtility.FromJson<UnityCommand>(jsonMessage);
            HandleCommand(command);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error parsing command: {e.Message}");
            SendError($"Command parsing failed: {e.Message}");
        }
    }
    
    private void HandleCommand(UnityCommand command)
    {
        switch (command.command)
        {
            case "changeExpression":
                ChangeExpression(command.parameters["expression"].ToString());
                break;
            case "changeEnvironment":
                ChangeEnvironment(command.parameters["environment"].ToString());
                break;
            // Add more command handlers...
        }
    }
    
    private void SendError(string message)
    {
        var errorMessage = new
        {
            type = "error",
            data = new { message = message }
        };
        messageManager.SendMessageToFlutter(JsonUtility.ToJson(errorMessage));
    }
}

[System.Serializable]
public class UnityCommand
{
    public string command;
    public System.Collections.Generic.Dictionary<string, object> parameters;
    public long timestamp;
}
```

## Flutter Implementation

The communication is handled automatically by the Unity provider. You can use it like this:

```dart
// Get Unity controller
final unityController = ref.read(unityControllerProvider.notifier);

// Send commands
unityController.changeExpression('happy');
unityController.changeEnvironment('cozy_room');
unityController.startTalking(emotion: 'excited');

// Check Unity state
final isReady = unityController.isReady;
final currentExpression = ref.watch(currentExpressionProvider);
```

## Debugging

### Enable Debug Mode
```dart
// In Flutter
unityController.sendCommand('setDebugMode', {'enabled': true});
```

### Test Communication
```dart
// Send ping to test connection
unityController.pingUnity();

// Request current state
unityController.requestUnityState();
```

### Common Issues

1. **Commands not received**: Check GameObject name is "GameManager"
2. **JSON parsing errors**: Ensure Unity script handles JSON properly
3. **Messages not sent**: Verify UnityMessageManager is attached
4. **Performance issues**: Use batch commands for multiple operations

## Best Practices

1. **Error Handling**: Always wrap Unity operations in try-catch
2. **State Sync**: Keep Flutter and Unity state synchronized
3. **Performance**: Use batch commands for multiple operations
4. **Debugging**: Enable debug mode during development
5. **Validation**: Validate parameters before sending to Unity

## Testing

Use the Unity demo page to test communication:

```dart
// Navigate to Unity demo
Navigator.push(context, MaterialPageRoute(
  builder: (context) => const UnityDemoPage(),
));
```

This provides a testing interface for all Unity communication features.
